using System.Text.Json.Serialization;

namespace HotPreview.SharedModel.Protocol;

public record PreviewInfo(
    [property: <PERSON>son<PERSON>ropertyName("previewType")] string PreviewType,
    [property: <PERSON>sonPropertyName("name")] string Name,
    [property: <PERSON>son<PERSON>ropertyName("displayName")] string? DisplayName,
    [property: Json<PERSON>ropertyName("autoGenerated")] bool AutoGenerated
);
