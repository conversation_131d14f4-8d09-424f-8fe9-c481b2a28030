name: 🏭 Build

on:
  push:
    branches:
    - main
    - 'v*.*'
    - validate/*
  pull_request:
  workflow_dispatch:

env:
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
  BUILDCONFIGURATION: Release
  # codecov_token: 4dc9e7e2-6b01-4932-a180-847b52b43d35 # Get a new one from https://codecov.io/
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages/

jobs:
  build:
    name: 🏭 Build

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
        - windows-2022

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      with:
        fetch-depth: 0 # avoid shallow clone so nbgv can do its work.
    - name: ⚙ Install prerequisites
      run: |
        ./init.ps1 -UpgradePrerequisites
        dotnet --info

        # Print mono version if it is present.
        if (Get-Command mono -ErrorAction SilentlyContinue) {
          mono --version
        }
      shell: pwsh
    - name: ⚙️ Set pipeline variables based on source
      run: tools/variables/_define.ps1
      shell: pwsh
    - name: 🛠 Build
      run: dotnet build -t:build HotPreview-CI.slnf --no-restore -c ${{ env.BUILDCONFIGURATION }} /bl:"${{ runner.temp }}/_artifacts/build_logs/build.binlog"
    - name: 🛠 Build packages
      run: dotnet build Build-Packages.proj -c ${{ env.BUILDCONFIGURATION }} /bl:"${{ runner.temp }}/_artifacts/build_logs/build-packages.binlog"
    - name: 🧪 Test
      run: tools/dotnet-test-cloud.ps1 -Configuration ${{ env.BUILDCONFIGURATION }} -Agent ${{ runner.os }}
      shell: pwsh
    - name: 💅🏻 Verify formatted code
      run: dotnet format HotPreview-CI.slnf --verify-no-changes --no-restore
      shell: pwsh
    # - name: 📚 Verify docfx build
    #   run: dotnet docfx docfx/docfx.json --warningsAsErrors --disableGitFeatures
    - name: ⚙ Update pipeline variables based on build outputs
      run: tools/variables/_define.ps1
      shell: pwsh
    - name: 📢 Publish artifacts
      uses: ./.github/actions/publish-artifacts
      if: cancelled() == false
