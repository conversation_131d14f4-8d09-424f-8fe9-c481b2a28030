name: 📚 Docs

on:
  push:
    branches:
      - main

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  actions: read
  pages: write
  id-token: write
  contents: read

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: pages
  cancel-in-progress: false

jobs:
  publish-docs:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: windows-2022
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      with:
        fetch-depth: 0 # avoid shallow clone so nbgv can do its work.
    - name: ⚙ Install prerequisites
      run: ./init.ps1 -UpgradePrerequisites

    - run: dotnet docfx docs/docfx.json
      name: 📚 Generate documentation

    # TODO: Consider adding these steps in the future, to check for broken links and other errors
    #- name: 🔗 Markup Link Checker (mlc)
    #  uses: becheran/mlc@88c9db09b8dabab813a2edd13f955b36aa73657a # v0.22.0
    #    args: --do-not-warn-for-redirect-to https://learn.microsoft.com*,https://dotnet.microsoft.com/*,https://dev.azure.com/*,https://app.codecov.io/* -p docfx

    - name: Upload artifact
      uses: actions/upload-pages-artifact@56afc609e74202658d3ffba0e8f6dda462b719fa # v3
      with:
        path: docs/_site

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@d6db90164ac5ed86f2b6aed7e0febac5b3c0c03e # v4
