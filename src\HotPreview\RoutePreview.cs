namespace HotPreview;

/// <summary>
/// Represents a preview for a route-based navigation.
/// </summary>
public class RoutePreview(string route)
{
    public string Route { get; } = route;
}

/// <summary>
/// Represents a strongly-typed preview for a route-based navigation.
/// </summary>
/// <typeparam name="T">The type associated with this route preview.</typeparam>
public class RoutePreview<T>(string route) : RoutePreview(route) where T : class
{
}
