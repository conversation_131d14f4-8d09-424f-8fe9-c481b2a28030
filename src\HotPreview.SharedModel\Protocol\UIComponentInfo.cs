using System.Text.Json.Serialization;

namespace HotPreview.SharedModel.Protocol;

public record UIComponentInfo(
    [property: JsonPropertyName("name")] string Name,
    [property: <PERSON>sonPropertyName("uiComponentKind")] string UIComponentKindInfo,
    [property: <PERSON>son<PERSON>ropertyName("displayName")] string? DisplayName,
    [property: <PERSON>son<PERSON>ropertyName("previews")] PreviewInfo[] Previews
);
